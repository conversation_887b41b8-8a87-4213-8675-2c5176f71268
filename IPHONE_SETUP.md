# iPhone Syncthing Setup Guide

This guide covers setting up Syncthing on iPhone using the Möbius Sync app to sync with your existing MBP and Pi setup.

## Prerequisites

- MBP and Pi Syncthing setup completed (run `./deploy-all.sh`)
- iPhone with iOS 12.0 or later
- Access to the App Store

## Step 1: Install Möbius Sync

1. Open the App Store on your iPhone
2. Search for "Möbius Sync" (official Syncthing client for iOS)
3. Install the app (it may be a paid app)

**Alternative**: If Möbius Sync is not available, you can use other Syncthing-compatible apps like:
- Syncthing-Fork (if available)
- Any WebDAV client with Syncthing's WebDAV feature

## Step 2: Get Pi Device Information

Before configuring the iPhone, you need the Pi's device ID and connection details:

```bash
# Get Pi device ID (run this on your MBP)
ssh pi "grep 'device id.*pi' ~/.config/syncthing/config.xml"

# Or check the deploy-all.sh output for the Pi device ID:
# Pi: W6KJSD5-T5XGQJY-L37WXWZ-BKXCXNH-2NS4NOR-ZPMSW53-FPY7H7Q-TGQ====
```

## Step 3: Configure iPhone in Möbius Sync

1. **Open Möbius Sync** on your iPhone
2. **Add Device** (the Pi as introducer):
   - Device ID: `W6KJSD5-T5XGQJY-L37WXWZ-BKXCXNH-2NS4NOR-ZPMSW53-FPY7H7Q-TGQ====`
   - Device Name: `pi`
   - Address: Leave as `dynamic` (or try your Pi's IP if needed)
3. **Accept the connection** when prompted

## Step 4: Configure Folder Sharing

1. **Wait for folder invitation**: Since the Pi is configured as an introducer, it should automatically offer the "Notes" folder
2. **Accept the Notes folder** when prompted
3. **Choose local path**: Select where you want the Notes folder on your iPhone (usually in the app's document folder)

## Step 5: Verify Sync

1. **Check folder status** in Möbius Sync - it should show "Up to Date"
2. **Test sync**: Create a test file on your iPhone in the Notes folder
3. **Verify on other devices**: Check that the file appears on MBP (`~/Notes/`) and Pi (`~/Synced/Notes/`)

## Important Notes

### Device ID Management

⚠️ **Critical**: The iPhone device ID in your configs is currently set to:
```
4IC2HCY-ARNMNZQ-E335BPJ-MZPCYFA-O7IUXSF-M6UIYRY-MKDFICZ-UDBIRAV
```

**If you reinstall Möbius Sync or reset the app, this device ID will change!**

When this happens, you must:
1. Get the new device ID from Möbius Sync settings
2. Update both config files (`configs/mbp-config.xml` and `configs/pi-config.xml`)
3. Redeploy configs: `./deploy-all.sh`

### Troubleshooting

**Connection Issues:**
- Ensure all devices are on the same network or have internet access
- Check that the Pi is running: `ssh pi "systemctl --user status syncthing"`
- Verify MBP Syncthing is running: `pgrep -f syncthing`

**Sync Issues:**
- Check folder permissions on iPhone
- Verify the folder path is correct in Möbius Sync
- Look for sync conflicts in the app

**App-Specific Issues:**
- Möbius Sync may require background app refresh to be enabled
- Check iOS storage permissions for the app
- Ensure the app has network permissions

### Alternative Setup (Manual Device Addition)

If the automatic introducer setup doesn't work:

1. **Get iPhone device ID** from Möbius Sync settings
2. **Add iPhone to Pi manually**:
   ```bash
   # SSH to Pi and add device through web UI
   ssh pi
   # Open http://localhost:8384 in Pi's browser
   # Add device with iPhone's device ID
   ```
3. **Add iPhone to MBP manually**:
   ```bash
   # Open https://localhost:8384 in MBP's browser
   # Add device with iPhone's device ID
   ```

## Web UI Access

- **MBP**: https://localhost:8384
- **Pi**: https://pi:8384 (or http://[pi-ip]:8384)
- **iPhone**: Available within Möbius Sync app

## File Locations

- **MBP**: `~/Notes/` (symlink to `~/Repos/personal/notes/data`)
- **Pi**: `~/Synced/Notes/`
- **iPhone**: App-specific folder (configured in Möbius Sync)

## Security Notes

- All connections use TLS encryption
- Device certificates are auto-generated
- API keys are managed through 1Password
- Only trusted devices with correct device IDs can connect
