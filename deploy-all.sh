#!/usr/bin/env bash
set -euo pipefail

echo "🚀 Deploying Clean Syncthing Setup"
echo "=================================="
echo "✨ Static device IDs, auto-generated certificates, 1Password secrets"
echo ""

# Check if we have the required files
if [[ ! -f "configs/mbp-config.xml" ]] || [[ ! -f "configs/pi-config.xml" ]]; then
    echo "❌ Config files not found."
    exit 1
fi

# Check 1Password CLI
if ! command -v op >/dev/null 2>&1; then
    echo "⚠️  1Password CLI not found. API keys will not be injected."
    echo "   Install with: brew install 1password-cli"
    echo ""
fi

# Make scripts executable
chmod +x deploy-mbp.sh deploy-pi.sh

echo ""
echo "📱 Deploying to MBP..."
echo "====================="
./deploy-mbp.sh

echo ""
echo "🥧 Deploying to Pi..."
echo "===================="
./deploy-pi.sh

echo ""
echo "🧪 Testing synchronization..."
echo "============================="

# Create a test file
TEST_FILE="sync-test-$(date +%s).txt"
echo "Test sync from MBP at $(date)" > ~/Notes/"$TEST_FILE"

echo "Created test file: $TEST_FILE"
echo "Waiting 10 seconds for sync..."
sleep 10

# Check if file appeared on Pi
if ssh pi "test -f ~/Synced/Notes/$TEST_FILE"; then
    echo "✅ Sync test successful! File appeared on Pi."
    
    # Test reverse sync
    ssh pi "echo 'Test sync from Pi at \$(date)' > ~/Synced/Notes/sync-test-pi-\$(date +%s).txt"
    sleep 5
    
    if ls ~/Notes/sync-test-pi-* >/dev/null 2>&1; then
        echo "✅ Bidirectional sync working!"
    else
        echo "⚠️  Pi to MBP sync may need more time"
    fi
else
    echo "❌ Sync test failed. Check logs:"
    echo "  MBP: ~/.config/syncthing/syncthing.log"
    echo "  Pi: ssh pi 'systemctl --user status syncthing'"
fi

echo ""
echo "📋 Summary"
echo "=========="
echo "Device IDs:"
echo "  MBP:    GUFL7D6-FTJUTCC-Z5YVPNK-HCLYZ7D-PXFDL2U-AWHF2JV-3RF26XQ-V2Q===="
echo "  Pi:     W6KJSD5-T5XGQJY-L37WXWZ-BKXCXNH-2NS4NOR-ZPMSW53-FPY7H7Q-TGQ===="
echo "  iPhone: 4IC2HCY-ARNMNZQ-E335BPJ-MZPCYFA-O7IUXSF-M6UIYRY-MKDFICZ-UDBIRAV (manual setup)"
echo ""
echo "Web UIs:"
echo "  MBP: https://localhost:8384"
echo "  Pi:  https://pi:8384"
echo ""
echo "Folders:"
echo "  MBP: ~/Notes -> ~/Development/personal/notes/data"
echo "  Pi:  ~/Synced/Notes"
echo ""
echo "Certificates:"
echo "  Auto-generated by each device on first run"
echo ""
echo "🎉 Deployment complete!"
