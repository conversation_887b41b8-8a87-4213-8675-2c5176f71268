#!/usr/bin/env bash
set -euo pipefail

echo "Deploying Syncthing configuration to MBP..."

# Find Syncthing binary
SYNCTHING_BIN=""
if command -v syncthing >/dev/null 2>&1; then
    SYNCTHING_BIN="syncthing"
elif [[ -f "$(brew --prefix 2>/dev/null)/bin/syncthing" ]]; then
    SYNCTHING_BIN="$(brew --prefix)/bin/syncthing"
else
    # Try to find in Cellar
    SYNCTHING_BIN=$(find $(brew --prefix 2>/dev/null)/Cellar/syncthing/*/bin/syncthing 2>/dev/null | head -n1 || echo "")
fi

if [[ -z "$SYNCTHING_BIN" ]] || [[ ! -f "$SYNCTHING_BIN" ]]; then
    echo "❌ Syncthing binary not found. Please run ./install_syncthing_mbp.sh first"
    exit 1
fi

echo "✅ Using Syncthing binary: $SYNCTHING_BIN"

# Stop Syncthing if running
echo "Stopping Syncthing..."
pkill -f syncthing || true
sleep 2

# Create config directory
mkdir -p ~/.config/syncthing

# Backup existing config if it exists
if [[ -f ~/.config/syncthing/config.xml ]]; then
    echo "Backing up existing config..."
    cp ~/.config/syncthing/config.xml ~/.config/syncthing/config.xml.backup.$(date +%Y%m%d_%H%M%S)
fi

# Copy and process config with 1Password injection (force overwrite)
echo "Deploying new config with 1Password API key injection (force overwrite)..."
if command -v op >/dev/null 2>&1; then
    # Use op CLI to inject the API key and force overwrite
    op inject -i configs/mbp-config.xml -o ~/.config/syncthing/config.xml
    echo "✅ API key injected from 1Password"
else
    echo "⚠️  1Password CLI not found, forcibly copying config as-is"
    cp -f configs/mbp-config.xml ~/.config/syncthing/config.xml
fi

# Set proper permissions (certificates will be auto-generated by Syncthing)
chmod 644 ~/.config/syncthing/config.xml

# Create the Notes symlink if it doesn't exist
if [[ ! -L ~/Notes ]]; then
    echo "Creating ~/Notes symlink..."
    # Check if the target directory exists (try multiple possible locations)
    if [[ -d ~/Repos/personal/notes/data ]]; then
        ln -sf ~/Repos/personal/notes/data ~/Notes
        echo "✅ Notes symlink created: ~/Notes -> ~/Repos/personal/notes/data"
    elif [[ -d ~/Development/personal/notes/data ]]; then
        ln -sf ~/Development/personal/notes/data ~/Notes
        echo "✅ Notes symlink created: ~/Notes -> ~/Development/personal/notes/data"
    else
        echo "⚠️  Target directory not found in expected locations"
        echo "   Creating ~/Notes as a regular directory instead"
        mkdir -p ~/Notes
    fi
elif [[ -L ~/Notes ]]; then
    # Check if existing symlink is valid
    if [[ -d "$(readlink ~/Notes)" ]]; then
        echo "✅ Notes symlink already exists: $(readlink ~/Notes)"
    else
        echo "⚠️  Notes symlink exists but target is missing: $(readlink ~/Notes)"
        echo "   Fixing symlink..."
        rm ~/Notes
        if [[ -d ~/Repos/personal/notes/data ]]; then
            ln -sf ~/Repos/personal/notes/data ~/Notes
            echo "✅ Fixed Notes symlink: ~/Notes -> ~/Repos/personal/notes/data"
        elif [[ -d ~/Development/personal/notes/data ]]; then
            ln -sf ~/Development/personal/notes/data ~/Notes
            echo "✅ Fixed Notes symlink: ~/Notes -> ~/Development/personal/notes/data"
        else
            mkdir -p ~/Notes
            echo "⚠️  Created ~/Notes as directory (no valid target found)"
        fi
    fi
else
    echo "⚠️  ~/Notes exists but is not a symlink"
fi

# Start Syncthing with --home option
echo "Starting Syncthing with --home option..."
nohup "$SYNCTHING_BIN" --home ~/.config/syncthing --no-browser --no-restart > ~/.config/syncthing/syncthing.log 2>&1 &

# Wait a moment for startup
sleep 3

# Check if it's running
if pgrep -f "syncthing.*--home" > /dev/null; then
    echo "✅ Syncthing started successfully!"
    echo "Web UI: https://localhost:8384"
    echo "Log file: ~/.config/syncthing/syncthing.log"
else
    echo "❌ Failed to start Syncthing"
    echo "Check log: ~/.config/syncthing/syncthing.log"
    exit 1
fi

echo "MBP deployment complete!"
