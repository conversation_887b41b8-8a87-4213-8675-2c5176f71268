#!/usr/bin/env bash
set -euo pipefail

echo "Deploying Syncthing configuration to MBP..."

# Stop Syncthing if running
echo "Stopping Syncthing..."
pkill -f syncthing || true
sleep 2

# Create config directory
mkdir -p ~/.config/syncthing

# Backup existing config if it exists
if [[ -f ~/.config/syncthing/config.xml ]]; then
    echo "Backing up existing config..."
    cp ~/.config/syncthing/config.xml ~/.config/syncthing/config.xml.backup.$(date +%Y%m%d_%H%M%S)
fi

# Copy and process config with 1Password injection (force overwrite)
echo "Deploying new config with 1Password API key injection (force overwrite)..."
if command -v op >/dev/null 2>&1; then
    # Use op CLI to inject the API key and force overwrite
    op inject -i configs/mbp-config.xml -o ~/.config/syncthing/config.xml
    echo "✅ API key injected from 1Password"
else
    echo "⚠️  1Password CLI not found, forcibly copying config as-is"
    cp -f configs/mbp-config.xml ~/.config/syncthing/config.xml
fi

# Set proper permissions (certificates will be auto-generated by Syncthing)
chmod 644 ~/.config/syncthing/config.xml

# Create the Notes symlink if it doesn't exist
if [[ ! -L ~/Notes ]]; then
    echo "Creating ~/Notes symlink..."
    ln -sf ~/Development/personal/notes/data ~/Notes
fi

# Start Syncthing with --home option
echo "Starting Syncthing with --home option..."
nohup syncthing --home ~/.config/syncthing --no-browser --no-restart > ~/.config/syncthing/syncthing.log 2>&1 &

# Wait a moment for startup
sleep 3

# Check if it's running
if pgrep -f "syncthing.*--home" > /dev/null; then
    echo "✅ Syncthing started successfully!"
    echo "Web UI: https://localhost:8384"
    echo "Log file: ~/.config/syncthing/syncthing.log"
else
    echo "❌ Failed to start Syncthing"
    echo "Check log: ~/.config/syncthing/syncthing.log"
    exit 1
fi

echo "MBP deployment complete!"
